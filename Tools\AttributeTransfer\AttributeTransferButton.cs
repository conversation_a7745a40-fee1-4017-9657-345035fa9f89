using System;
using ArcGIS.Desktop.Framework.Contracts;
using XIAOFUTools.Tools.Authorization;

namespace XIAOFUTools.Tools.AttributeTransfer
{
    /// <summary>
    /// 属性传递[字段]按钮
    /// </summary>
    internal class AttributeTransferButton : But<PERSON>
    {
        protected override void OnClick()
        {
            try
            {
                // 检查授权
                if (!AuthorizationChecker.CheckAuthorizationWithPrompt("属性传递工具"))
                {
                    return;
                }

                // 打开属性传递窗口
                AttributeTransferView.ShowDialog();
            }
            catch (Exception ex)
            {
                ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show($"打开属性传递窗口时出错: {ex.Message}", "错误");
            }
        }
    }
}
