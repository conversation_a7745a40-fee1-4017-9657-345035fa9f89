using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using ArcGIS.Core.Data;
using ArcGIS.Desktop.Framework;
using ArcGIS.Desktop.Framework.Threading.Tasks;
using ArcGIS.Desktop.Mapping;

namespace XIAOFUTools.Tools.AttributeTransfer
{
    /// <summary>
    /// 属性传递功能的视图模型
    /// </summary>
    public class AttributeTransferViewModel : INotifyPropertyChanged
    {
        #region 私有字段
        private bool _isProcessing = false;
        private string _statusMessage = "就绪";
        private int _progressValue = 0;
        private bool _progressVisible = false;
        
        private LayerInfo _selectedPrimaryLayer;
        private LayerInfo _selectedSecondaryLayer;
        private FieldInfo _selectedPrimaryField;
        private FieldInfo _selectedSecondaryField;
        private bool _transferFromPrimaryToSecondary = true;
        
        private ObservableCollection<LayerInfo> _layerList = new ObservableCollection<LayerInfo>();
        private ObservableCollection<FieldInfo> _primaryFieldList = new ObservableCollection<FieldInfo>();
        private ObservableCollection<FieldInfo> _secondaryFieldList = new ObservableCollection<FieldInfo>();
        private ObservableCollection<FieldMappingItem> _fieldMappingList = new ObservableCollection<FieldMappingItem>();
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否正在处理
        /// </summary>
        public bool IsProcessing
        {
            get => _isProcessing;
            set => SetProperty(ref _isProcessing, value);
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 进度值
        /// </summary>
        public int ProgressValue
        {
            get => _progressValue;
            set => SetProperty(ref _progressValue, value);
        }

        /// <summary>
        /// 进度条是否可见
        /// </summary>
        public bool ProgressVisible
        {
            get => _progressVisible;
            set => SetProperty(ref _progressVisible, value);
        }

        /// <summary>
        /// 图层列表
        /// </summary>
        public ObservableCollection<LayerInfo> LayerList
        {
            get => _layerList;
            set => SetProperty(ref _layerList, value);
        }

        /// <summary>
        /// 选中的主要素图层/表
        /// </summary>
        public LayerInfo SelectedPrimaryLayer
        {
            get => _selectedPrimaryLayer;
            set
            {
                if (SetProperty(ref _selectedPrimaryLayer, value))
                {
                    LoadPrimaryFields();
                    UpdateFieldMapping();
                }
            }
        }

        /// <summary>
        /// 选中的副要素图层/表
        /// </summary>
        public LayerInfo SelectedSecondaryLayer
        {
            get => _selectedSecondaryLayer;
            set
            {
                if (SetProperty(ref _selectedSecondaryLayer, value))
                {
                    LoadSecondaryFields();
                    UpdateFieldMapping();
                }
            }
        }

        /// <summary>
        /// 主连接字段列表
        /// </summary>
        public ObservableCollection<FieldInfo> PrimaryFieldList
        {
            get => _primaryFieldList;
            set => SetProperty(ref _primaryFieldList, value);
        }

        /// <summary>
        /// 副连接字段列表
        /// </summary>
        public ObservableCollection<FieldInfo> SecondaryFieldList
        {
            get => _secondaryFieldList;
            set => SetProperty(ref _secondaryFieldList, value);
        }

        /// <summary>
        /// 选中的主连接字段
        /// </summary>
        public FieldInfo SelectedPrimaryField
        {
            get => _selectedPrimaryField;
            set
            {
                if (SetProperty(ref _selectedPrimaryField, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// 选中的副连接字段
        /// </summary>
        public FieldInfo SelectedSecondaryField
        {
            get => _selectedSecondaryField;
            set
            {
                if (SetProperty(ref _selectedSecondaryField, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// 传递方向：主→副
        /// </summary>
        public bool TransferFromPrimaryToSecondary
        {
            get => _transferFromPrimaryToSecondary;
            set
            {
                if (SetProperty(ref _transferFromPrimaryToSecondary, value))
                {
                    UpdateFieldMapping();
                }
            }
        }

        /// <summary>
        /// 传递方向：副→主
        /// </summary>
        public bool TransferFromSecondaryToPrimary
        {
            get => !_transferFromPrimaryToSecondary;
            set => TransferFromPrimaryToSecondary = !value;
        }

        /// <summary>
        /// 字段映射列表
        /// </summary>
        public ObservableCollection<FieldMappingItem> FieldMappingList
        {
            get => _fieldMappingList;
            set => SetProperty(ref _fieldMappingList, value);
        }
        #endregion

        #region 命令
        public ICommand RefreshLayersCommand { get; }
        public ICommand SelectAllCommand { get; }
        public ICommand InvertSelectionCommand { get; }
        public ICommand ExecuteTransferCommand { get; }
        public ICommand CancelCommand { get; }
        #endregion

        #region 构造函数
        public AttributeTransferViewModel()
        {
            RefreshLayersCommand = new RelayCommand(RefreshLayers);
            SelectAllCommand = new RelayCommand(SelectAll);
            InvertSelectionCommand = new RelayCommand(InvertSelection);
            ExecuteTransferCommand = new RelayCommand(ExecuteTransfer, CanExecuteTransfer);
            CancelCommand = new RelayCommand(Cancel);

            LoadLayers();
            StatusMessage = "请选择数据源和连接字段";
        }
        #endregion

        #region 私有方法
        private async void LoadLayers()
        {
            await QueuedTask.Run(() =>
            {
                var layers = new List<LayerInfo>();

                // 获取地图中的所有图层和表
                var map = MapView.Active?.Map;
                if (map != null)
                {
                    // 添加要素图层（支持shapefile、geodatabase等）
                    foreach (var layer in map.GetLayersAsFlattenedList().OfType<FeatureLayer>())
                    {
                        layers.Add(new LayerInfo
                        {
                            Name = layer.Name,
                            Layer = layer,
                            LayerType = LayerType.FeatureLayer,
                            DataSourceType = GetDataSourceType(layer)
                        });
                    }

                    // 添加独立表（支持geodatabase表格、Excel等）
                    foreach (var table in map.StandaloneTables)
                    {
                        layers.Add(new LayerInfo
                        {
                            Name = table.Name,
                            Layer = table,
                            LayerType = LayerType.StandaloneTable,
                            DataSourceType = GetDataSourceType(table)
                        });
                    }
                }

                LayerList.Clear();
                foreach (var layer in layers)
                {
                    LayerList.Add(layer);
                }

                if (!layers.Any())
                {
                    StatusMessage = "当前地图中没有可用的图层或表，请先添加数据";
                }
                else
                {
                    StatusMessage = $"已加载 {layers.Count} 个图层/表";
                }
            });
        }

        /// <summary>
        /// 获取数据源类型
        /// </summary>
        private string GetDataSourceType(object layer)
        {
            try
            {
                if (layer is FeatureLayer featureLayer)
                {
                    using (var table = featureLayer.GetTable())
                    {
                        var datastore = table.GetDatastore();
                        if (datastore is Geodatabase)
                        {
                            return "Geodatabase";
                        }
                        else if (datastore is FileSystemDatastore)
                        {
                            return "Shapefile";
                        }
                    }
                }
                else if (layer is StandaloneTable standaloneTable)
                {
                    using (var table = standaloneTable.GetTable())
                    {
                        var datastore = table.GetDatastore();
                        if (datastore is Geodatabase)
                        {
                            return "Geodatabase Table";
                        }
                        else if (datastore is FileSystemDatastore)
                        {
                            var connectionPath = datastore.GetPath();
                            var pathString = connectionPath.ToString();
                            if (pathString.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase) ||
                                pathString.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
                            {
                                return "Excel";
                            }
                            return "File Table";
                        }
                    }
                }
            }
            catch
            {
                // 如果获取数据源类型失败，返回未知
            }

            return "Unknown";
        }

        private void RefreshLayers()
        {
            LoadLayers();
        }

        private async void LoadPrimaryFields()
        {
            if (SelectedPrimaryLayer?.Layer == null) return;

            await QueuedTask.Run(() =>
            {
                var fields = new List<FieldInfo>();
                
                if (SelectedPrimaryLayer.Layer is FeatureLayer featureLayer)
                {
                    using (var table = featureLayer.GetTable())
                    {
                        var tableDefinition = table.GetDefinition();
                        foreach (var field in tableDefinition.GetFields())
                        {
                            fields.Add(new FieldInfo
                            {
                                Name = field.Name,
                                AliasName = field.AliasName,
                                FieldType = field.FieldType,
                                Length = field.Length
                            });
                        }
                    }
                }
                else if (SelectedPrimaryLayer.Layer is StandaloneTable standaloneTable)
                {
                    using (var table = standaloneTable.GetTable())
                    {
                        var tableDefinition = table.GetDefinition();
                        foreach (var field in tableDefinition.GetFields())
                        {
                            fields.Add(new FieldInfo
                            {
                                Name = field.Name,
                                AliasName = field.AliasName,
                                FieldType = field.FieldType,
                                Length = field.Length
                            });
                        }
                    }
                }

                PrimaryFieldList.Clear();
                foreach (var field in fields)
                {
                    PrimaryFieldList.Add(field);
                }
            });
        }

        private async void LoadSecondaryFields()
        {
            if (SelectedSecondaryLayer?.Layer == null) return;

            await QueuedTask.Run(() =>
            {
                var fields = new List<FieldInfo>();
                
                if (SelectedSecondaryLayer.Layer is FeatureLayer featureLayer)
                {
                    using (var table = featureLayer.GetTable())
                    {
                        var tableDefinition = table.GetDefinition();
                        foreach (var field in tableDefinition.GetFields())
                        {
                            fields.Add(new FieldInfo
                            {
                                Name = field.Name,
                                AliasName = field.AliasName,
                                FieldType = field.FieldType,
                                Length = field.Length
                            });
                        }
                    }
                }
                else if (SelectedSecondaryLayer.Layer is StandaloneTable standaloneTable)
                {
                    using (var table = standaloneTable.GetTable())
                    {
                        var tableDefinition = table.GetDefinition();
                        foreach (var field in tableDefinition.GetFields())
                        {
                            fields.Add(new FieldInfo
                            {
                                Name = field.Name,
                                AliasName = field.AliasName,
                                FieldType = field.FieldType,
                                Length = field.Length
                            });
                        }
                    }
                }

                SecondaryFieldList.Clear();
                foreach (var field in fields)
                {
                    SecondaryFieldList.Add(field);
                }
            });
        }

        private void UpdateFieldMapping()
        {
            FieldMappingList.Clear();

            if (TransferFromPrimaryToSecondary && SelectedPrimaryLayer != null && SecondaryFieldList.Any())
            {
                foreach (var field in PrimaryFieldList)
                {
                    // 跳过系统字段
                    if (IsSystemField(field.Name))
                        continue;

                    var targetField = FindMatchingField(field, SecondaryFieldList);

                    FieldMappingList.Add(new FieldMappingItem
                    {
                        IsSelected = targetField != null,
                        SourceField = field,
                        TargetFields = new ObservableCollection<FieldInfo>(SecondaryFieldList.Where(f => !IsSystemField(f.Name))),
                        SelectedTargetField = targetField
                    });
                }
            }
            else if (!TransferFromPrimaryToSecondary && SelectedSecondaryLayer != null && PrimaryFieldList.Any())
            {
                foreach (var field in SecondaryFieldList)
                {
                    // 跳过系统字段
                    if (IsSystemField(field.Name))
                        continue;

                    var targetField = FindMatchingField(field, PrimaryFieldList);

                    FieldMappingList.Add(new FieldMappingItem
                    {
                        IsSelected = targetField != null,
                        SourceField = field,
                        TargetFields = new ObservableCollection<FieldInfo>(PrimaryFieldList.Where(f => !IsSystemField(f.Name))),
                        SelectedTargetField = targetField
                    });
                }
            }
        }

        /// <summary>
        /// 判断是否为系统字段
        /// </summary>
        private bool IsSystemField(string fieldName)
        {
            var systemFields = new[] { "OBJECTID", "SHAPE", "SHAPE_Length", "SHAPE_Area", "GlobalID", "ESRI_OID" };
            return systemFields.Any(sf => string.Equals(sf, fieldName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 查找匹配的字段
        /// </summary>
        private FieldInfo FindMatchingField(FieldInfo sourceField, ObservableCollection<FieldInfo> targetFields)
        {
            // 首先尝试精确匹配字段名
            var exactMatch = targetFields.FirstOrDefault(f =>
                string.Equals(f.Name, sourceField.Name, StringComparison.OrdinalIgnoreCase));
            if (exactMatch != null)
                return exactMatch;

            // 然后尝试匹配别名
            var aliasMatch = targetFields.FirstOrDefault(f =>
                !string.IsNullOrEmpty(f.AliasName) &&
                string.Equals(f.AliasName, sourceField.AliasName, StringComparison.OrdinalIgnoreCase));
            if (aliasMatch != null)
                return aliasMatch;

            // 最后尝试匹配相同类型的字段
            var typeMatch = targetFields.FirstOrDefault(f =>
                f.FieldType == sourceField.FieldType &&
                !IsSystemField(f.Name));

            return typeMatch;
        }

        private void SelectAll()
        {
            foreach (var item in FieldMappingList)
            {
                // 只选择有目标字段的项
                if (item.SelectedTargetField != null)
                {
                    item.IsSelected = true;
                }
            }

            StatusMessage = $"已选择 {FieldMappingList.Count(x => x.IsSelected)} 个字段进行传递";
        }

        private void InvertSelection()
        {
            foreach (var item in FieldMappingList)
            {
                // 只对有目标字段的项进行反选
                if (item.SelectedTargetField != null)
                {
                    item.IsSelected = !item.IsSelected;
                }
            }

            StatusMessage = $"已选择 {FieldMappingList.Count(x => x.IsSelected)} 个字段进行传递";
        }

        private bool CanExecuteTransfer()
        {
            return !IsProcessing && 
                   SelectedPrimaryLayer != null && 
                   SelectedSecondaryLayer != null &&
                   SelectedPrimaryField != null &&
                   SelectedSecondaryField != null &&
                   FieldMappingList.Any(x => x.IsSelected && x.SelectedTargetField != null);
        }

        private async void ExecuteTransfer()
        {
            try
            {
                IsProcessing = true;
                ProgressVisible = true;
                ProgressValue = 0;
                StatusMessage = "开始属性传递...";

                // 验证输入
                if (!ValidateInput())
                {
                    return;
                }

                // 获取选中的字段映射
                var selectedMappings = FieldMappingList.Where(x => x.IsSelected && x.SelectedTargetField != null).ToList();
                if (!selectedMappings.Any())
                {
                    StatusMessage = "请选择要传递的字段";
                    return;
                }

                await QueuedTask.Run(async () =>
                {
                    try
                    {
                        // 执行属性传递
                        await PerformAttributeTransfer(selectedMappings);

                        StatusMessage = $"属性传递完成，共处理 {selectedMappings.Count} 个字段";
                    }
                    catch (Exception ex)
                    {
                        StatusMessage = $"属性传递失败: {ex.Message}";
                        ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show($"属性传递失败: {ex.Message}", "错误");
                    }
                });
            }
            catch (Exception ex)
            {
                StatusMessage = $"执行失败: {ex.Message}";
                ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show($"执行失败: {ex.Message}", "错误");
            }
            finally
            {
                IsProcessing = false;
                ProgressVisible = false;
                ProgressValue = 0;
            }
        }

        private bool ValidateInput()
        {
            if (SelectedPrimaryLayer == null)
            {
                StatusMessage = "请选择主要素图层/表";
                ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show("请选择主要素图层/表", "验证失败");
                return false;
            }

            if (SelectedSecondaryLayer == null)
            {
                StatusMessage = "请选择副要素图层/表";
                ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show("请选择副要素图层/表", "验证失败");
                return false;
            }

            if (SelectedPrimaryField == null)
            {
                StatusMessage = "请选择主连接字段";
                ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show("请选择主连接字段", "验证失败");
                return false;
            }

            if (SelectedSecondaryField == null)
            {
                StatusMessage = "请选择副连接字段";
                ArcGIS.Desktop.Framework.Dialogs.MessageBox.Show("请选择副连接字段", "验证失败");
                return false;
            }

            return true;
        }

        private async Task PerformAttributeTransfer(List<FieldMappingItem> selectedMappings)
        {
            // 获取源表和目标表
            Table sourceTable = null;
            Table targetTable = null;

            try
            {
                if (TransferFromPrimaryToSecondary)
                {
                    sourceTable = GetTableFromLayer(SelectedPrimaryLayer);
                    targetTable = GetTableFromLayer(SelectedSecondaryLayer);
                }
                else
                {
                    sourceTable = GetTableFromLayer(SelectedSecondaryLayer);
                    targetTable = GetTableFromLayer(SelectedPrimaryLayer);
                }

                if (sourceTable == null || targetTable == null)
                {
                    throw new Exception("无法获取数据表");
                }

                // 创建字段映射字典
                var fieldMappings = selectedMappings.ToDictionary(
                    m => m.SourceField.Name,
                    m => m.SelectedTargetField.Name
                );

                // 获取连接字段名称
                string sourceJoinField = TransferFromPrimaryToSecondary ?
                    SelectedPrimaryField.Name : SelectedSecondaryField.Name;
                string targetJoinField = TransferFromPrimaryToSecondary ?
                    SelectedSecondaryField.Name : SelectedPrimaryField.Name;

                // 执行属性传递
                await TransferAttributes(sourceTable, targetTable, sourceJoinField, targetJoinField, fieldMappings);
            }
            finally
            {
                sourceTable?.Dispose();
                targetTable?.Dispose();
            }
        }

        private Table GetTableFromLayer(LayerInfo layerInfo)
        {
            if (layerInfo.Layer is FeatureLayer featureLayer)
            {
                return featureLayer.GetTable();
            }
            else if (layerInfo.Layer is StandaloneTable standaloneTable)
            {
                return standaloneTable.GetTable();
            }
            return null;
        }

        private async Task TransferAttributes(Table sourceTable, Table targetTable,
            string sourceJoinField, string targetJoinField, Dictionary<string, string> fieldMappings)
        {
            StatusMessage = "正在读取源数据...";
            ProgressValue = 10;

            // 创建源数据字典
            var sourceData = new Dictionary<object, Dictionary<string, object>>();

            using (var sourceCursor = sourceTable.Search())
            {
                while (sourceCursor.MoveNext())
                {
                    using (var row = sourceCursor.Current)
                    {
                        var joinValue = row[sourceJoinField];
                        if (joinValue != null && joinValue != DBNull.Value)
                        {
                            var rowData = new Dictionary<string, object>();
                            foreach (var mapping in fieldMappings)
                            {
                                try
                                {
                                    var value = row[mapping.Key];
                                    rowData[mapping.Value] = value;
                                }
                                catch
                                {
                                    // 如果字段不存在，跳过
                                    rowData[mapping.Value] = null;
                                }
                            }
                            sourceData[joinValue] = rowData;
                        }
                    }
                }
            }

            StatusMessage = $"已读取 {sourceData.Count} 条源数据，正在更新目标数据...";
            ProgressValue = 50;

            // 更新目标数据
            int updatedCount = 0;
            var editOperation = new ArcGIS.Desktop.Editing.EditOperation();
            editOperation.Name = "属性传递";

            // 收集需要更新的行和值
            var rowsToUpdate = new List<(long objectId, Dictionary<string, object> values)>();

            using (var targetCursor = targetTable.Search())
            {
                while (targetCursor.MoveNext())
                {
                    using (var row = targetCursor.Current)
                    {
                        var joinValue = row[targetJoinField];
                        if (joinValue != null && joinValue != DBNull.Value && sourceData.ContainsKey(joinValue))
                        {
                            var sourceRowData = sourceData[joinValue];
                            var valuesToUpdate = new Dictionary<string, object>();

                            foreach (var mapping in fieldMappings)
                            {
                                if (sourceRowData.ContainsKey(mapping.Value))
                                {
                                    var newValue = sourceRowData[mapping.Value];
                                    var currentValue = row[mapping.Value];

                                    // 检查值是否不同
                                    if (!Equals(currentValue, newValue))
                                    {
                                        valuesToUpdate[mapping.Value] = newValue;
                                    }
                                }
                            }

                            if (valuesToUpdate.Any())
                            {
                                var objectId = row.GetObjectID();
                                rowsToUpdate.Add((objectId, valuesToUpdate));
                            }
                        }
                    }
                }
            }

            // 执行批量更新
            if (rowsToUpdate.Any())
            {
                editOperation.Callback(context =>
                {
                    foreach (var (objectId, values) in rowsToUpdate)
                    {
                        using (var rowBuffer = targetTable.CreateRowBuffer())
                        {
                            // 获取要更新的行
                            var queryFilter = new ArcGIS.Core.Data.QueryFilter();
                            queryFilter.WhereClause = $"OBJECTID = {objectId}";

                            using (var cursor = targetTable.Search(queryFilter))
                            {
                                if (cursor.MoveNext())
                                {
                                    using (var row = cursor.Current)
                                    {
                                        // 更新字段值
                                        foreach (var kvp in values)
                                        {
                                            row[kvp.Key] = kvp.Value;
                                        }
                                        row.Store();
                                        updatedCount++;
                                    }
                                }
                            }
                        }
                    }
                }, targetTable);
            }

            StatusMessage = "正在保存更改...";
            ProgressValue = 90;

            // 执行编辑操作
            if (editOperation.IsEmpty)
            {
                StatusMessage = "没有找到需要更新的数据";
            }
            else
            {
                var result = await editOperation.ExecuteAsync();
                if (result)
                {
                    StatusMessage = $"属性传递完成，已更新 {updatedCount} 条记录";
                }
                else
                {
                    throw new Exception("保存更改失败");
                }
            }

            ProgressValue = 100;
        }

        private void Cancel()
        {
            // 关闭窗口
            AttributeTransferView.CloseWindow();
        }
        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
        #endregion
    }
}
