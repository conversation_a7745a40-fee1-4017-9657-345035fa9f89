<UserControl x:Class="XIAOFUTools.Tools.AttributeTransfer.AttributeTransferView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:XIAOFUTools.Tools.AttributeTransfer"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1000">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/XIAOFUTools;component/Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Background="{StaticResource BackgroundBrush}" 
            BorderBrush="#CDCDCD" BorderThickness="1" CornerRadius="4">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题 -->
            <TextBlock Grid.Row="0" Text="属性传递[字段]" 
                       Style="{StaticResource HeaderTextBlockStyle}"/>

            <!-- 数据源选择区域 -->
            <GroupBox Grid.Row="1" Header="数据源选择" Margin="0,0,0,12" Padding="12">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 主要素图层/表选择 -->
                    <StackPanel Grid.Row="0" Grid.Column="0">
                        <TextBlock Text="主要素图层/表:" Style="{StaticResource FormLabelStyle}" Margin="0,0,0,4"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <ComboBox Grid.Column="0"
                                     ItemsSource="{Binding LayerList}"
                                     SelectedItem="{Binding SelectedPrimaryLayer, Mode=TwoWay}"
                                     DisplayMemberPath="DisplayName">
                                <ComboBox.Style>
                                    <Style TargetType="ComboBox" BasedOn="{StaticResource CompactComboBoxStyle}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsProcessing}" Value="True">
                                                <Setter Property="IsEnabled" Value="False"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ComboBox.Style>
                            </ComboBox>
                            <Button Grid.Column="1" Width="22" Height="22" Margin="5,0,0,0"
                                    Style="{StaticResource DefaultButtonStyle}"
                                    Command="{Binding RefreshLayersCommand}"
                                    ToolTip="刷新图层列表"
                                    VerticalAlignment="Center">
                                <TextBlock Text="⟲" FontSize="14" FontWeight="Bold"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Button>
                        </Grid>
                    </StackPanel>

                    <!-- 副要素图层/表选择 -->
                    <StackPanel Grid.Row="0" Grid.Column="2">
                        <TextBlock Text="副要素图层/表:" Style="{StaticResource FormLabelStyle}" Margin="0,0,0,4"/>
                        <ComboBox ItemsSource="{Binding LayerList}"
                                 SelectedItem="{Binding SelectedSecondaryLayer, Mode=TwoWay}"
                                 DisplayMemberPath="DisplayName">
                            <ComboBox.Style>
                                <Style TargetType="ComboBox" BasedOn="{StaticResource CompactComboBoxStyle}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsProcessing}" Value="True">
                                            <Setter Property="IsEnabled" Value="False"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ComboBox.Style>
                        </ComboBox>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- 字段连接配置 -->
            <GroupBox Grid.Row="2" Header="字段连接配置" Margin="0,0,0,12" Padding="12">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 主连接字段 -->
                    <StackPanel Grid.Row="0" Grid.Column="0">
                        <TextBlock Text="主连接字段:" Style="{StaticResource FormLabelStyle}" Margin="0,0,0,4"/>
                        <ComboBox ItemsSource="{Binding PrimaryFieldList}"
                                 SelectedItem="{Binding SelectedPrimaryField, Mode=TwoWay}"
                                 DisplayMemberPath="DisplayName">
                            <ComboBox.Style>
                                <Style TargetType="ComboBox" BasedOn="{StaticResource CompactComboBoxStyle}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsProcessing}" Value="True">
                                            <Setter Property="IsEnabled" Value="False"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ComboBox.Style>
                        </ComboBox>
                    </StackPanel>

                    <!-- 副连接字段 -->
                    <StackPanel Grid.Row="0" Grid.Column="2">
                        <TextBlock Text="副连接字段:" Style="{StaticResource FormLabelStyle}" Margin="0,0,0,4"/>
                        <ComboBox ItemsSource="{Binding SecondaryFieldList}"
                                 SelectedItem="{Binding SelectedSecondaryField, Mode=TwoWay}"
                                 DisplayMemberPath="DisplayName">
                            <ComboBox.Style>
                                <Style TargetType="ComboBox" BasedOn="{StaticResource CompactComboBoxStyle}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsProcessing}" Value="True">
                                            <Setter Property="IsEnabled" Value="False"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ComboBox.Style>
                        </ComboBox>
                    </StackPanel>

                    <!-- 传递方向控制 -->
                    <StackPanel Grid.Row="0" Grid.Column="4" VerticalAlignment="Center">
                        <TextBlock Text="传递方向:" Style="{StaticResource FormLabelStyle}" Margin="0,0,0,4"/>
                        <StackPanel>
                            <RadioButton Content="主 → 副" 
                                        IsChecked="{Binding TransferFromPrimaryToSecondary}"
                                        Margin="0,2"/>
                            <RadioButton Content="副 → 主" 
                                        IsChecked="{Binding TransferFromSecondaryToPrimary}"
                                        Margin="0,2"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- 字段映射表格区域 -->
            <GroupBox Grid.Row="3" Header="字段映射" Padding="12">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 控制按钮 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,8">
                        <Button Content="全选" 
                                Style="{StaticResource DefaultButtonStyle}"
                                Command="{Binding SelectAllCommand}"
                                Width="60" Height="24" Margin="0,0,8,0"/>
                        <Button Content="反选" 
                                Style="{StaticResource DefaultButtonStyle}"
                                Command="{Binding InvertSelectionCommand}"
                                Width="60" Height="24"/>
                    </StackPanel>

                    <!-- 字段映射表格 -->
                    <DataGrid Grid.Row="1" 
                             ItemsSource="{Binding FieldMappingList}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserReorderColumns="False"
                             CanUserResizeRows="False"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             IsReadOnly="False">
                        <DataGrid.Columns>
                            <!-- 传递复选框列 -->
                            <DataGridCheckBoxColumn Header="传递" 
                                                   Binding="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                                   Width="50"/>
                            
                            <!-- 源字段名称列 -->
                            <DataGridTextColumn Header="源字段" 
                                               Binding="{Binding SourceField.DisplayName}"
                                               Width="200"
                                               IsReadOnly="True"/>
                            
                            <!-- 字段类型列 -->
                            <DataGridTextColumn Header="字段类型" 
                                               Binding="{Binding SourceField.FieldTypeDisplayName}"
                                               Width="100"
                                               IsReadOnly="True"/>
                            
                            <!-- 字段长度列 -->
                            <DataGridTextColumn Header="字段长度" 
                                               Binding="{Binding SourceField.LengthDisplay}"
                                               Width="80"
                                               IsReadOnly="True"/>
                            
                            <!-- 目标字段下拉选择列 -->
                            <DataGridTemplateColumn Header="目标字段" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding SelectedTargetField.DisplayName}"
                                                  VerticalAlignment="Center"
                                                  Margin="4,0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                                <DataGridTemplateColumn.CellEditingTemplate>
                                    <DataTemplate>
                                        <ComboBox ItemsSource="{Binding TargetFields}"
                                                 SelectedItem="{Binding SelectedTargetField, UpdateSourceTrigger=PropertyChanged}"
                                                 DisplayMemberPath="DisplayName"
                                                 Margin="0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellEditingTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </GroupBox>

            <!-- 进度条 -->
            <ProgressBar Grid.Row="4" 
                        Height="20" 
                        Margin="0,8,0,8"
                        Value="{Binding ProgressValue}"
                        Visibility="{Binding ProgressVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>

            <!-- 状态栏和按钮区域 -->
            <Grid Grid.Row="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 状态消息 -->
                <TextBlock Grid.Column="0" 
                          Text="{Binding StatusMessage}" 
                          VerticalAlignment="Center"
                          Style="{StaticResource LabelTextBlockStyle}"/>

                <!-- 按钮区域 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="执行传递" 
                            Style="{StaticResource ExecuteButtonStyle}"
                            Command="{Binding ExecuteTransferCommand}"
                            Width="80" Height="28" Margin="0,0,8,0"/>
                    <Button Content="取消" 
                            Style="{StaticResource CancelButtonStyle}"
                            Command="{Binding CancelCommand}"
                            Width="60" Height="28"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</UserControl>
