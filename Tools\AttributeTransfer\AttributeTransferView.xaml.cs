using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using ArcGIS.Desktop.Framework.Controls;

namespace XIAOFUTools.Tools.AttributeTransfer
{
    /// <summary>
    /// AttributeTransferView.xaml 的交互逻辑
    /// </summary>
    public partial class AttributeTransferView : UserControl
    {
        private static AttributeTransferView _view = null;
        private static ProWindow _window = null;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public AttributeTransferView()
        {
            InitializeComponent();
            DataContext = new AttributeTransferViewModel();
        }

        /// <summary>
        /// 显示窗口
        /// </summary>
        public static void ShowDialog()
        {
            if (_window != null)
            {
                _window.Close();
                _window = null;
                _view = null;
            }

            _view = new AttributeTransferView();

            _window = new ProWindow
            {
                Content = _view,
                Title = "属性传递[字段]",
                Width = 1000,
                Height = 700,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                ResizeMode = ResizeMode.CanResize,
                MinWidth = 800,
                MinHeight = 600
            };

            _window.ShowDialog();
        }

        /// <summary>
        /// 关闭窗口
        /// </summary>
        public static void CloseWindow()
        {
            _window?.Close();
            _window = null;
            _view = null;
        }
    }

    /// <summary>
    /// 布尔值反转转换器
    /// </summary>
    public class BooleanInverseConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }
}
