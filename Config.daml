<?xml version="1.0" encoding="utf-8"?>
<ArcGIS defaultAssembly="XIAOFUTools.dll" defaultNamespace="XIAOFUTools" xmlns="http://schemas.esri.com/DADF/Registry" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://schemas.esri.com/DADF/Registry file:///D:/RUANJIAN/ArcGIS/Pro/bin/ArcGIS.Desktop.Framework.xsd">
  <!-- 插件基本信息 -->
  <AddInInfo id="{c1ef0bf8-3aaa-4583-9176-c77c409d3e5d}" version="1.0.1" desktopVersion="3.5.57366" product="ArcGISPro">
    <Name>XIAOFU工具箱</Name>
    <Description>XIAOFU工具箱 - ArcGIS Pro扩展工具集</Description>
    <Image>Images\Toolbox_32.png</Image>
    <Author>XIAOFU</Author>
    <Company>Acme</Company>
    <Date>2025/5/16 10:04:37</Date>
    <Subject>Framework</Subject>
    <!--
    可选主题: Content, Framework, Editing, Geodatabase, Geometry,
             Geoprocessing, Layouts, Map Authoring, Map Exploration
    -->
  </AddInInfo>
  <modules>
    <insertModule id="XIAOFUTools_Module" className="Module1" autoLoad="true" caption="XIAOFU工具箱">
      <!-- ============================== 选项卡定义（最顶层） ============================== -->
      <tabs>
        <tab id="XIAOFUTools_Tab1" caption="XIAOFU工具箱" keytip="X">
          <group refID="XIAOFUTools_CommonGroup" />
          <group refID="XIAOFUTools_EditGroup" />
          <group refID="XIAOFUTools_AnalysisGroup" />
          <group refID="XIAOFUTools_ConvertGroup" />
          <group refID="XIAOFUTools_DataProcessingGroup" />
          <group refID="XIAOFUTools_UserGroup" />
          <group refID="XIAOFUTools_QuickAccessGroup" />
        </tab>
      </tabs>
      <!-- ============================== 分组定义（中间层） ============================== -->
      <groups>
        <!-- 通用工具组 -->
        <group id="XIAOFUTools_CommonGroup" caption="通用工具" appearsOnAddInTab="false">
          <buttonPalette refID="XIAOFUTools_CommonButtonPalette" />
          <buttonPalette refID="XIAOFUTools_Common2ButtonPalette" />
          <button refID="XIAOFUTools_ViewAreaButton" />
          <inlineGallery refID="XIAOFUTools_PresetLayersGallery" />
        </group>
        <!-- 编辑工具组 -->
        <group id="XIAOFUTools_EditGroup" caption="编辑工具" appearsOnAddInTab="false">
          <buttonPalette refID="XIAOFUTools_EditButtonPalette" />
          <buttonPalette refID="XIAOFUTools_DynamicToolsButtonPalette" />
        </group>
        <!-- 分析工具组 -->
        <group id="XIAOFUTools_AnalysisGroup" caption="分析工具" appearsOnAddInTab="false">
          <buttonPalette refID="XIAOFUTools_AnalysisButtonPalette" />
        </group>
        <!-- 转换工具组 -->
        <group id="XIAOFUTools_ConvertGroup" caption="转换工具" appearsOnAddInTab="false">
          <buttonPalette refID="XIAOFUTools_ConvertButtonPalette" />
          <buttonPalette refID="XIAOFUTools_CoordinateButtonPalette" />
          <buttonPalette refID="XIAOFUTools_OutputButtonPalette" />
          <buttonPalette refID="XIAOFUTools_OvertureLoaderButton" />
        </group>
        <!-- 数据处理组 -->
        <group id="XIAOFUTools_DataProcessingGroup" caption="数据处理" appearsOnAddInTab="false">
          <buttonPalette refID="XIAOFUTools_GraphicsProcessingButtonPalette" />
          <buttonPalette refID="XIAOFUTools_DataAnalysisButtonPalette" />
        </group>
        <!-- 用户组 -->
        <group id="XIAOFUTools_UserGroup" caption="用户" appearsOnAddInTab="false">
          <button refID="XIAOFUTools_AIAssistantButton" />
          <buttonPalette refID="XIAOFUTools_UserConfigButtonPalette" />
        </group>
        <!-- 快速访问组 -->
        <group id="XIAOFUTools_QuickAccessGroup" caption="快速访问" appearsOnAddInTab="false">
          <!-- 添加按钮面板"快速访问" -->
          <splitButton refID="esri_mapping_exploreSplitButton" />
          <splitButton refID="esri_editing_selectToolPalette" />
          <splitButton refID="esri_mapping_clearSelectionButton" />
          <splitButton refID="esri_reports_navigateContext" />
          <splitButton refID="esri_layouts_selectContext" />
          <splitButton refID="esri_mapping_measureSplitButton" />
        </group>
      </groups>
      <!-- ============================== 按钮面板定义（较低层） ============================== -->
      <palettes>
        <!-- 通用工具按钮面板1 -->
        <buttonPalette id="XIAOFUTools_CommonButtonPalette" caption="通用" dropDown="true" menuStyle="false">
          <button refID="XIAOFUTools_BatchAddDataButton" />
          <button refID="XIAOFUTools_GroupNumberingButton" />
          <button refID="XIAOFUTools_ChineseNumberingButton" />
          <!-- 在此添加更多按钮引用 -->
        </buttonPalette>
        <!-- 通用工具按钮面板2 -->
        <buttonPalette id="XIAOFUTools_Common2ButtonPalette" caption="通用2" dropDown="true" menuStyle="false">
          <button refID="XIAOFUTools_BatchLayerClipButton" />
          <button refID="XIAOFUTools_FieldCopyToolButton" />
          <button refID="XIAOFUTools_RangeClipToolButton" />
          <button refID="XIAOFUTools_BoundaryPointGeneratorButton" />
          <button refID="XIAOFUTools_BatchProjectionDefinitionButton" />
          <button refID="XIAOFUTools_BatchGeometryRepairButton" />
          <!-- 在此添加更多按钮引用 -->
        </buttonPalette>
        <!-- 编辑工具按钮面板 -->
        <buttonPalette id="XIAOFUTools_EditButtonPalette" caption="编辑工具" dropDown="true" menuStyle="false">
          <!-- 在此添加编辑工具按钮引用 -->
        </buttonPalette>
        <!-- 动态工具按钮面板 -->
        <buttonPalette id="XIAOFUTools_DynamicToolsButtonPalette" caption="动态工具" dropDown="true" menuStyle="false">
          <tool refID="XIAOFUTools_AreaSplitTool" />
        </buttonPalette>
        <!-- 计算工具按钮面板 -->
        <buttonPalette id="XIAOFUTools_AnalysisButtonPalette" caption="计算" dropDown="true" menuStyle="false">
          <button refID="XIAOFUTools_AreaCalculatorButton" />
          <!-- 在此添加计算工具按钮引用 -->
        </buttonPalette>
        <!-- 转换工具按钮面板 -->
        <buttonPalette id="XIAOFUTools_ConvertButtonPalette" caption="文本" dropDown="true" menuStyle="false">
          <button refID="XIAOFUTools_FeatureToTxtButton" />
          <button refID="XIAOFUTools_TxtToFeatureButton" />
          <!-- 在此添加转换工具按钮引用 -->
        </buttonPalette>
        <!-- 坐标转换按钮面板 -->
        <buttonPalette id="XIAOFUTools_CoordinateButtonPalette" caption="坐标" dropDown="true" menuStyle="false">
          <button refID="XIAOFUTools_SpecialCoordinateTransformButton" />
          <!-- 在此添加坐标转换工具按钮引用 -->
        </buttonPalette>
        <!-- 输出工具按钮面板 -->
        <buttonPalette id="XIAOFUTools_OutputButtonPalette" caption="输出" dropDown="true" menuStyle="false">
          <button refID="XIAOFUTools_DownloadOnlineImageryButton" />
          <button refID="XIAOFUTools_ExportLayoutButton" />
          <!-- 在此添加输出工具按钮引用 -->
        </buttonPalette>
        <!-- 图形检查按钮面板 -->
        <buttonPalette id="XIAOFUTools_GraphicsProcessingButtonPalette" caption="图形检查" dropDown="true" menuStyle="false">
          <button refID="XIAOFUTools_NodeDistanceCheckButton" />
          <button refID="XIAOFUTools_OverlapCheckButton" />
          <button refID="XIAOFUTools_GapCheckButton" />
          <!-- 在此添加更多图形检查工具按钮引用 -->
        </buttonPalette>
        <!-- 数据分析按钮面板 -->
        <buttonPalette id="XIAOFUTools_DataAnalysisButtonPalette" caption="数据分析" dropDown="true" menuStyle="false">
          <button refID="XIAOFUTools_AttributeTransferButton" />
          <!-- 在此添加更多数据分析工具按钮引用 -->
        </buttonPalette>
        <!-- 用户配置按钮面板 -->
        <buttonPalette id="XIAOFUTools_UserConfigButtonPalette" caption="配置" dropDown="true" menuStyle="false">
			<button refID="XIAOFUTools_SettingsButton" />
			<button refID="XIAOFUTools_AuthorizationButton" />
			<button refID="XIAOFUTools_AboutButton" />

          <!-- 在此添加用户配置工具按钮引用 -->
        </buttonPalette>
      </palettes>
      <!-- ============================== 图库定义 ============================== -->
      <galleries>
        <!-- 添加预设图层图库 -->
        <gallery id="XIAOFUTools_PresetLayersGallery" caption="添加预设图层" className="XIAOFUTools.Tools.PresetLayers.PresetLayersGallery" keytip="PL" itemsInRow="3" itemWidth="60" itemHeight="120" showItemCaption="true" showItemCaptionBelow="true" resizable="false" loadingMessage="加载中..." dropDownHeight="250" dropDownWidth="320" menuStyle="true" dataTemplateFile="Tools\PresetLayers\PresetLayersGalleryTemplate.xaml" templateID="PresetLayerItemTemplate" smallImage="Images\PresetLayers_16.png" largeImage="Images\PresetLayers_32.png">
          <tooltip heading="添加预设图层">
            从预设图层库中选择并添加图层到地图
            <disabledText>无权限使用添加预设图层功能</disabledText></tooltip>
        </gallery>
      </galleries>
      <!-- ============================== 停靠窗格定义 ============================== -->
      <dockPanes>
        <!-- 批量添加数据停靠窗格 -->
        <dockPane id="XIAOFUTools_BatchAddDataDockPane" caption="批量添加数据" className="XIAOFUTools.Tools.BatchAddData.BatchAddDataDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 按字段批量裁剪要素图层停靠窗格 -->
        <dockPane id="XIAOFUTools_BatchLayerClipDockPane" caption="按字段批量裁剪要素图层" className="XIAOFUTools.Tools.BatchLayerClip.BatchLayerClipDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 生成四至坐标点停靠窗格 -->
        <dockPane id="XIAOFUTools_BoundaryPointGeneratorDockPane" caption="生成四至坐标点" className="XIAOFUTools.Tools.BoundaryPointGenerator.BoundaryPointGeneratorDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 地块中文编号停靠窗格 -->
        <dockPane id="XIAOFUTools_ChineseNumberingDockPane" caption="地块中文编号" className="XIAOFUTools.Tools.ChineseNumbering.ChineseNumberingDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 要素顺序编号停靠窗格 -->
        <dockPane id="XIAOFUTools_GroupNumberingDockPane" caption="要素顺序编号" className="XIAOFUTools.Tools.GroupNumbering.GroupNumberingDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 根据范围批量裁剪要素图层停靠窗格 -->
        <dockPane id="XIAOFUTools_RangeClipToolDockPane" caption="根据范围批量裁剪要素图层" className="XIAOFUTools.Tools.RangeClipTool.RangeClipToolDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 计算面积停靠窗格 -->
        <dockPane id="XIAOFUTools_AreaCalculatorDockPane" caption="计算面积" className="XIAOFUTools.Tools.AreaCalculator.AreaCalculatorDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 批量定义投影停靠窗格 -->
        <dockPane id="XIAOFUTools_BatchProjectionDefinitionDockPane" caption="批量定义投影" className="XIAOFUTools.Tools.BatchProjectionDefinition.BatchProjectionDefinitionDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 批量修复几何停靠窗格 -->
        <dockPane id="XIAOFUTools_BatchGeometryRepairDockPane" caption="批量修复几何" className="XIAOFUTools.Tools.BatchGeometryRepair.BatchGeometryRepairDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 查看面积停靠窗格 -->
        <dockPane id="XIAOFUTools_ViewAreaDockPane" caption="查看面积" className="XIAOFUTools.Tools.ViewArea.ViewAreaDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- AI助手停靠窗格 -->
        <dockPane id="XIAOFUTools_AIAssistantDockPane" caption="AI助手" className="XIAOFUTools.Tools.AIAssistant.AIAssistantDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 授权管理停靠窗格 -->
        <dockPane id="XIAOFUTools_AuthorizationDockPane" caption="授权管理" className="XIAOFUTools.Tools.Authorization.AuthorizationDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 设置停靠窗格 -->
        <dockPane id="XIAOFUTools_SettingsDockPane" caption="设置" className="XIAOFUTools.Tools.Settings.SettingsDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 要素类转TXT停靠窗格 -->
        <dockPane id="XIAOFUTools_FeatureToTxtDockPane" caption="要素类转TXT" className="XIAOFUTools.Tools.FeatureToTxt.FeatureToTxtDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 下载在线影像停靠窗格 -->
        <dockPane id="XIAOFUTools_DownloadOnlineImageryDockPane" caption="下载在线影像" className="XIAOFUTools.Tools.DownloadOnlineImagery.DownloadOnlineImageryDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 导出布局停靠窗格 -->
        <dockPane id="XIAOFUTools_ExportLayoutDockPane" caption="导出布局" className="XIAOFUTools.Tools.ExportLayout.ExportLayoutDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- TXT转SHP停靠窗格 -->
        <dockPane id="XIAOFUTools_TxtToFeatureDockPane" caption="TXT转SHP" className="XIAOFUTools.Tools.TxtToFeature.TxtToFeatureDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 特殊坐标转换停靠窗格 -->
        <dockPane id="XIAOFUTools_SpecialCoordinateTransformDockPane" caption="特殊坐标转换" className="XIAOFUTools.Tools.SpecialCoordinateTransform.SpecialCoordinateTransformDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 节点距离检查工具停靠窗格 -->
        <dockPane id="XIAOFUTools_NodeDistanceCheckDockPane" caption="节点距离检查工具" className="XIAOFUTools.Tools.NodeDistanceCheck.NodeDistanceCheckDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 图形重叠检查工具停靠窗格 -->
        <dockPane id="XIAOFUTools_OverlapCheckDockPane" caption="图形重叠检查工具" className="XIAOFUTools.Tools.OverlapCheck.OverlapCheckDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- 缝隙检查工具停靠窗格 -->
        <dockPane id="XIAOFUTools_GapCheckDockPane" caption="缝隙检查工具" className="XIAOFUTools.Tools.GapCheck.GapCheckDockPane" dock="group" dockWith="esri_core_projectDockPane"></dockPane>
        <!-- Overture Maps 数据加载器停靠窗格 -->
        <dockPane id="XIAOFUTools_OvertureLoaderDockPane" caption="Overture Maps 数据加载器" className="XIAOFUTools.Tools.OvertureLoader.Views.WizardDockpaneViewModel" dock="group" dockWith="esri_core_projectDockPane">
          <content className="XIAOFUTools.Tools.OvertureLoader.Views.WizardDockpaneView" />
        </dockPane>
      </dockPanes>
      <!-- ============================== 控件定义（最底层） ============================== -->
      <controls>
        <!-- 批量添加数据按钮 -->
        <button id="XIAOFUTools_BatchAddDataButton" caption="批量添加数据" className="XIAOFUTools.Tools.BatchAddData.BatchAddDataButton" loadOnClick="true" smallImage="Images\BatchAddData_16.png" largeImage="Images\BatchAddData_32.png">
          <tooltip heading="批量添加数据">
            扫描文件夹中的GIS数据并批量添加到地图
            <disabledText>无权限使用批量添加数据工具</disabledText></tooltip>
        </button>
        <!-- 分组编号按钮 -->
        <button id="XIAOFUTools_GroupNumberingButton" caption="要素顺序编号" className="XIAOFUTools.Tools.GroupNumbering.GroupNumberingButton" loadOnClick="true" smallImage="Images\GroupNumbering_16.png" largeImage="Images\GroupNumbering_32.png">
          <tooltip heading="要素顺序编号">
            按指定分组字段对要素进行顺序编号
            <disabledText>无权限使用要素顺序编号工具</disabledText></tooltip>
        </button>
        <!-- 地块中文编号按钮 -->
        <button id="XIAOFUTools_ChineseNumberingButton" caption="地块中文编号" className="XIAOFUTools.Tools.ChineseNumbering.ChineseNumberingButton" loadOnClick="true" smallImage="Images\ChineseNumbering_16.png" largeImage="Images\ChineseNumbering_32.png">
          <tooltip heading="地块中文编号">
            使用中文数字（一、二、三...）对要素进行编号
            <disabledText>无权限使用地块中文编号工具</disabledText></tooltip>
        </button>
        <!-- 按字段批量裁剪按钮 -->
        <button id="XIAOFUTools_BatchLayerClipButton" caption="按字段批量裁剪要素" className="XIAOFUTools.Tools.BatchLayerClip.BatchLayerClipButton" loadOnClick="true" smallImage="Images\BatchLayerClip_16.png" largeImage="Images\BatchLayerClip_32.png">
          <tooltip heading="按字段批量裁剪">
            根据字段值批量裁剪要素并导出为Shapefile
            <disabledText>无权限使用按字段批量裁剪工具</disabledText></tooltip>
        </button>
        <!-- 字段复制工具按钮 -->
        <button id="XIAOFUTools_FieldCopyToolButton" caption="字段复制工具" className="XIAOFUTools.Tools.FieldCopyTool.FieldCopyToolButton" loadOnClick="true" smallImage="Images\FieldCopyTool_16.png" largeImage="Images\FieldCopyTool_32.png">
          <tooltip heading="字段复制工具">
            将源数据的字段复制到目标图层
            <disabledText>无权限使用字段复制工具</disabledText></tooltip>
        </button>
        <!-- 根据范围批量裁剪要素图层按钮 -->
        <button id="XIAOFUTools_RangeClipToolButton" caption="根据范围批量裁剪要素图层" className="XIAOFUTools.Tools.RangeClipTool.RangeClipToolButton" loadOnClick="true" smallImage="Images\RangeClipTool_16.png" largeImage="Images\RangeClipTool_32.png">
          <tooltip heading="根据范围批量裁剪要素图层">
            根据范围图层的字段值批量裁剪要素图层
            <disabledText>无权限使用根据范围批量裁剪要素图层工具</disabledText></tooltip>
        </button>
        <!-- 生成四至坐标点按钮 -->
        <button id="XIAOFUTools_BoundaryPointGeneratorButton" caption="生成四至坐标点" className="XIAOFUTools.Tools.BoundaryPointGenerator.BoundaryPointGeneratorButton" loadOnClick="true" smallImage="Images\BoundaryPointGenerator_16.png" largeImage="Images\BoundaryPointGenerator_32.png">
          <tooltip heading="生成四至坐标点">
            根据输入面要素生成四至坐标点（最东、最西、最南、最北点）
            <disabledText>无权限使用生成四至坐标点工具</disabledText></tooltip>
        </button>
        <!-- 计算面积按钮 -->
        <button id="XIAOFUTools_AreaCalculatorButton" caption="计算面积" className="XIAOFUTools.Tools.AreaCalculator.AreaCalculatorButton" loadOnClick="true" smallImage="Images\AreaCalculator_16.png" largeImage="Images\AreaCalculator_32.png">
          <tooltip heading="计算面积">
            计算面要素的面积并写入指定字段
            <disabledText>无权限使用计算面积工具</disabledText></tooltip>
        </button>
        <!-- 批量定义投影按钮 -->
        <button id="XIAOFUTools_BatchProjectionDefinitionButton" caption="批量定义投影" className="XIAOFUTools.Tools.BatchProjectionDefinition.BatchProjectionDefinitionButton" loadOnClick="true" smallImage="Images\BatchProjectionDefinition_16.png" largeImage="Images\BatchProjectionDefinition_32.png">
          <tooltip heading="批量定义投影">
            为多个图层批量定义投影坐标系
            <disabledText>无权限使用批量定义投影工具</disabledText></tooltip>
        </button>
        <!-- 批量修复几何按钮 -->
        <button id="XIAOFUTools_BatchGeometryRepairButton" caption="批量修复几何" className="XIAOFUTools.Tools.BatchGeometryRepair.BatchGeometryRepairButton" loadOnClick="true" smallImage="Images\BatchGeometryRepair_16.png" largeImage="Images\BatchGeometryRepair_32.png">
          <tooltip heading="批量修复几何">
            批量修复选中图层的几何错误
            <disabledText>无权限使用批量修复几何工具</disabledText></tooltip>
        </button>
        <!-- 查看面积按钮 -->
        <button id="XIAOFUTools_ViewAreaButton" caption="查看面积" className="XIAOFUTools.Tools.ViewArea.ViewAreaButton" loadOnClick="true" smallImage="Images\ViewArea_16.png" largeImage="Images\ViewArea_32.png">
          <tooltip heading="查看面积">
            查看当前选中要素的面积和长度信息
            <disabledText>无权限使用查看面积工具</disabledText></tooltip>
        </button>
        <!-- AI助手按钮 -->
        <button id="XIAOFUTools_AIAssistantButton" caption="AI助手" className="XIAOFUTools.Tools.AIAssistant.AIAssistantButton" loadOnClick="true" smallImage="Images\AIAssistant_16.png" largeImage="Images\AIAssistant_32.png">
          <tooltip heading="AI助手">
            打开AI助手，获得GIS相关问题的智能解答
            <disabledText>无权限使用AI助手功能</disabledText></tooltip>
        </button>
        <!-- 授权管理按钮 -->
        <button id="XIAOFUTools_AuthorizationButton" caption="授权" className="XIAOFUTools.Tools.Authorization.AuthorizationButton" loadOnClick="true" smallImage="Images\Authorization_16.png" largeImage="Images\Authorization_32.png">
          <tooltip heading="授权管理">
            打开授权管理面板，管理软件授权状态
            <disabledText>无权限使用授权管理功能</disabledText></tooltip>
        </button>
        <!-- 设置按钮 -->
        <button id="XIAOFUTools_SettingsButton" caption="设置" className="XIAOFUTools.Tools.Settings.SettingsButton" loadOnClick="true" smallImage="Images\Settings_16.png" largeImage="Images\Settings_32.png">
          <tooltip heading="设置">
            打开XIAOFU工具箱设置面板
            <disabledText>无权限使用设置功能</disabledText></tooltip>
        </button>
        <!-- 关于按钮 -->
        <button id="XIAOFUTools_AboutButton" caption="关于" className="XIAOFUTools.Tools.About.AboutButton" loadOnClick="true" smallImage="Images\About_16.png" largeImage="Images\About_32.png">
          <tooltip heading="关于">
            查看XIAOFU工具箱版本信息和作者信息
            <disabledText>无权限使用关于功能</disabledText></tooltip>
        </button>
        <!-- 要素类转TXT按钮 -->
        <button id="XIAOFUTools_FeatureToTxtButton" caption="要素类转TXT" className="XIAOFUTools.Tools.FeatureToTxt.FeatureToTxtButton" loadOnClick="true" smallImage="Images\FeatureToTxt_16.png" largeImage="Images\FeatureToTxt_32.png">
          <tooltip heading="要素类转TXT">
            根据输入的面要素获取折点坐标输出为txt
            <disabledText>无权限使用要素类转TXT工具</disabledText></tooltip>
        </button>
        <!-- TXT转SHP按钮 -->
        <button id="XIAOFUTools_TxtToFeatureButton" caption="TXT转SHP" className="XIAOFUTools.Tools.TxtToFeature.TxtToFeatureButton" loadOnClick="true" smallImage="Images\TxtToFeature_16.png" largeImage="Images\TxtToFeature_32.png">
          <tooltip heading="TXT转SHP">
            将包含坐标信息的TXT文件转换为Shapefile要素类
            <disabledText>无权限使用TXT转SHP工具</disabledText></tooltip>
        </button>
        <!-- 下载在线影像按钮 -->
        <button id="XIAOFUTools_DownloadOnlineImageryButton" caption="下载在线影像" className="XIAOFUTools.Tools.DownloadOnlineImagery.DownloadOnlineImageryButton" loadOnClick="true" smallImage="Images\DownloadOnlineImagery_16.png" largeImage="Images\DownloadOnlineImagery_32.png">
          <tooltip heading="下载在线影像">
            根据要素图层范围下载在线影像并可选择合并
            <disabledText>无权限使用下载在线影像工具</disabledText></tooltip>
        </button>
        <!-- 导出布局按钮 -->
        <button id="XIAOFUTools_ExportLayoutButton" caption="导出布局" className="XIAOFUTools.Tools.ExportLayout.ExportLayoutButton" loadOnClick="true" smallImage="Images\ExportLayout_16.png" largeImage="Images\ExportLayout_32.png">
          <tooltip heading="导出布局">
            批量导出布局为PDF、TIF、JPG、PNG等格式
            <disabledText>无权限使用导出布局工具</disabledText></tooltip>
        </button>
        <!-- 特殊坐标转换按钮 -->
        <button id="XIAOFUTools_SpecialCoordinateTransformButton" caption="特殊坐标转换" className="XIAOFUTools.Tools.SpecialCoordinateTransform.SpecialCoordinateTransformButton" loadOnClick="true" smallImage="Images\SpecialCoordinateTransform_16.png" largeImage="Images\SpecialCoordinateTransform_32.png">
          <tooltip heading="特殊坐标转换">
            支持WGS84、GCJ02、BD09坐标系之间的相互转换
            <disabledText>无权限使用特殊坐标转换工具</disabledText></tooltip>
        </button>
        <!-- 启动Overture按钮 -->
        <button id="XIAOFUTools_OvertureLoaderButton" caption="启动Overture" className="XIAOFUTools.Tools.OvertureLoader.Views.WizardDockpaneShowButton" loadOnClick="true" smallImage="Images\Overture16.png" largeImage="Images\Overture32.png" condition="esri_mapping_mapPane">
          <tooltip heading="Overture Maps 数据加载器">
            启动 Overture Maps 数据加载器以导入 GeoParquet 数据
            <disabledText>目前无法启动 Overture Maps 数据加载器</disabledText></tooltip>
        </button>
        <!-- 自定义范围工具 -->
        <tool id="XIAOFUTools_CustomExtentTool" className="XIAOFUTools.Tools.OvertureLoader.Views.CustomExtentTool" caption="绘制自定义范围" loadOnClick="true" smallImage="pack://application:,,,/ArcGIS.Desktop.Resources;component/Images/SelectTool16.png" largeImage="pack://application:,,,/ArcGIS.Desktop.Resources;component/Images/SelectTool32.png" condition="esri_mapping_mapPane" keytip="E">
          <tooltip heading="绘制自定义范围">
            在地图上绘制矩形以定义自定义数据范围
            <disabledText>必须打开地图才能使用此工具</disabledText></tooltip>
        </tool>
        <!-- 面积分割工具 -->
        <tool id="XIAOFUTools_AreaSplitTool" className="XIAOFUTools.Tools.AreaSplit.AreaSplitTool" caption="面积分割" loadOnClick="true" smallImage="Images\AreaSplit_16.png" largeImage="Images\AreaSplit_32.png" condition="esri_mapping_mapPane" keytip="A">
          <tooltip heading="面积分割">
            动态面积分割工具，支持实时预览分割效果
            <disabledText>必须打开地图才能使用此工具</disabledText></tooltip>
        </tool>
        <!-- 节点距离检查工具按钮 -->
        <button id="XIAOFUTools_NodeDistanceCheckButton" caption="节点距离检查工具" className="XIAOFUTools.Tools.NodeDistanceCheck.NodeDistanceCheckButton" loadOnClick="true" smallImage="Images\NodeDistanceChecker_16.png" largeImage="Images\NodeDistanceChecker_32.png">
          <tooltip heading="节点距离检查工具">
            检查面要素图层中节点之间的距离，输出符合条件的线要素
            <disabledText>无权限使用节点距离检查工具</disabledText></tooltip>
        </button>
        <!-- 图形重叠检查工具按钮 -->
        <button id="XIAOFUTools_OverlapCheckButton" caption="图形重叠检查工具" className="XIAOFUTools.Tools.OverlapCheck.OverlapCheckButton" loadOnClick="true" smallImage="Images\OverlapCheck_16.png" largeImage="Images\OverlapCheck_32.png">
          <tooltip heading="图形重叠检查工具">
            检查面要素图层中的重叠区域，输出重叠的面要素
            <disabledText>无权限使用图形重叠检查工具</disabledText></tooltip>
        </button>
        <!-- 缝隙检查工具按钮 -->
        <button id="XIAOFUTools_GapCheckButton" caption="缝隙检查工具" className="XIAOFUTools.Tools.GapCheck.GapCheckButton" loadOnClick="true" smallImage="Images\GapCheck_16.png" largeImage="Images\GapCheck_32.png">
          <tooltip heading="缝隙检查工具">
            检查面要素图层中的缝隙，输出缝隙面要素
            <disabledText>无权限使用缝隙检查工具</disabledText></tooltip>
        </button>
        <!-- 此处可添加其他工具按钮 -->
      </controls>
      <menus></menus>
    </insertModule>
    <!-- ============================== 菜单定义 ============================== -->
    <updateModule refID="esri_mapping">
      <menus>
        <!-- 图层的右键菜单 -->
        <updateMenu refID="esri_mapping_layerContextMenu">
          <insertButton refID="XIAOFUTools_AreaCalculatorButton" insert="before" placeWith="esri_editing_table_openTablePaneButton" separator="true" />
          <!-- 在此添加图层右键菜单项 -->
        </updateMenu>
        <!-- 图层的右键菜单 (SHP等未注册图层) -->
        <updateMenu refID="esri_mapping_unregisteredLayerContextMenu">
          <insertButton refID="XIAOFUTools_AreaCalculatorButton" insert="before" placeWith="esri_editing_table_openTablePaneButton" separator="true" />
          <!-- 在此添加未注册图层右键菜单项 -->
        </updateMenu>
        <!-- 图层组的右键菜单 -->
        <updateMenu refID="esri_mapping_groupLayerContextMenu">
          <!-- 在此添加图层组右键菜单项 -->
        </updateMenu>
        <!-- 独立表的右键菜单 -->
        <updateMenu refID="esri_mapping_standaloneTableContextMenu">
          <!-- 在此添加独立表右键菜单项 -->
        </updateMenu>
        <!-- 栅格图层的右键菜单 -->
        <updateMenu refID="esri_mapping_rasterLayerContextMenu">
          <!-- 在此添加栅格图层右键菜单项 -->
        </updateMenu>
        <!-- 地图列表的右键菜单 -->
        <updateMenu refID="esri_mapping_mapContextMenu">
          <insertGallery refID="XIAOFUTools_PresetLayersGallery" insert="before" placeWith="esri_mapping_addDataButton" separator="true" />
          <insertButton refID="XIAOFUTools_BatchAddDataButton" insert="before" placeWith="esri_mapping_addDataButton" separator="false" />
          <!-- 在此添加地图右键菜单项 -->
        </updateMenu>
        <!-- 地图视图的右键菜单 -->
        <updateMenu refID="esri_mapping_popupToolContextMenu">
          <insertGallery refID="XIAOFUTools_PresetLayersGallery" insert="before" placeWith="esri_core_editPasteButton" separator="true" />
          <insertButton refID="XIAOFUTools_BatchAddDataButton" insert="before" placeWith="esri_core_editPasteButton" separator="false" />
          <!-- 在此添加地图视图右键菜单项 -->
        </updateMenu>
        <!-- 地图视图内选择的右键菜单 -->
        <updateMenu refID="esri_mapping_selection2DContextMenu">
          <insertButton refID="XIAOFUTools_ViewAreaButton" insert="before" placeWith="esri_core_editCopyButton" separator="true" />
          <insertGallery refID="XIAOFUTools_PresetLayersGallery" insert="before" placeWith="esri_core_editCopyButton" separator="false" />
          <!-- 在此添加更多选择右键菜单项 -->
        </updateMenu>
      </menus>
      <groups>
        <!-- 图层工具组更新 -->
        <updateGroup refID="esri_mapping_layerGroup">
          <insertGallery refID="XIAOFUTools_PresetLayersGallery" size="large" placeWith="esri_mapping_addDataSplitButton" insert="before" />
        </updateGroup>
      </groups>
    </updateModule>
    <!-- 编辑模块菜单定义 -->
    <updateModule refID="esri_editing_EditingModule">
      <menus>
        <!-- 要素图层数据菜单 -->
        <updateMenu refID="esri_editing_data">
          <!-- 在此添加要素图层数据菜单项 -->
        </updateMenu>
        <!-- 独立表数据菜单 -->
        <updateMenu refID="esri_editing_standalonetable_data_menu">
          <!-- 在此添加独立表数据菜单项 -->
        </updateMenu>
      </menus>
    </updateModule>
  </modules>
</ArcGIS>