using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using ArcGIS.Core.Data;
using ArcGIS.Desktop.Mapping;

namespace XIAOFUTools.Tools.AttributeTransfer
{
    /// <summary>
    /// 图层信息
    /// </summary>
    public class LayerInfo
    {
        /// <summary>
        /// 图层名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 图层对象
        /// </summary>
        public object Layer { get; set; }

        /// <summary>
        /// 图层类型
        /// </summary>
        public LayerType LayerType { get; set; }

        /// <summary>
        /// 数据源类型
        /// </summary>
        public string DataSourceType { get; set; }

        /// <summary>
        /// 显示名称（包含数据源类型）
        /// </summary>
        public string DisplayName => $"{Name} ({DataSourceType})";
    }

    /// <summary>
    /// 图层类型枚举
    /// </summary>
    public enum LayerType
    {
        FeatureLayer,
        StandaloneTable
    }

    /// <summary>
    /// 字段信息
    /// </summary>
    public class FieldInfo
    {
        /// <summary>
        /// 字段名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 字段别名
        /// </summary>
        public string AliasName { get; set; }

        /// <summary>
        /// 字段类型
        /// </summary>
        public FieldType FieldType { get; set; }

        /// <summary>
        /// 字段长度
        /// </summary>
        public int Length { get; set; }

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName => string.IsNullOrEmpty(AliasName) ? Name : $"{AliasName} ({Name})";

        /// <summary>
        /// 字段类型显示名称
        /// </summary>
        public string FieldTypeDisplayName
        {
            get
            {
                return FieldType switch
                {
                    FieldType.String => "文本",
                    FieldType.Integer => "整数",
                    FieldType.SmallInteger => "短整数",
                    FieldType.Double => "双精度",
                    FieldType.Single => "单精度",
                    FieldType.Date => "日期",
                    FieldType.OID => "对象ID",
                    FieldType.Geometry => "几何",
                    FieldType.Blob => "二进制",
                    FieldType.Raster => "栅格",
                    FieldType.GUID => "GUID",
                    FieldType.GlobalID => "全局ID",
                    FieldType.XML => "XML",
                    _ => FieldType.ToString()
                };
            }
        }

        /// <summary>
        /// 字段长度显示
        /// </summary>
        public string LengthDisplay => Length > 0 ? Length.ToString() : "-";
    }

    /// <summary>
    /// 字段映射项
    /// </summary>
    public class FieldMappingItem : INotifyPropertyChanged
    {
        private bool _isSelected;
        private FieldInfo _selectedTargetField;

        /// <summary>
        /// 是否选中传递
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (SetProperty(ref _isSelected, value))
                {
                    // 通知命令状态可能已更改
                    System.Windows.Input.CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// 源字段
        /// </summary>
        public FieldInfo SourceField { get; set; }

        /// <summary>
        /// 目标字段列表
        /// </summary>
        public ObservableCollection<FieldInfo> TargetFields { get; set; }

        /// <summary>
        /// 选中的目标字段
        /// </summary>
        public FieldInfo SelectedTargetField
        {
            get => _selectedTargetField;
            set
            {
                if (SetProperty(ref _selectedTargetField, value))
                {
                    // 通知命令状态可能已更改
                    System.Windows.Input.CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (System.Collections.Generic.EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
        #endregion
    }
}
