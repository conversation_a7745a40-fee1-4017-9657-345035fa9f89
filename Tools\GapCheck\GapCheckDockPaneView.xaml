<UserControl x:Class="XIAOFUTools.Tools.GapCheck.GapCheckDockPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="500" d:DesignWidth="450"
             Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <!-- 布尔值转可见性转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- 基本按钮样式 -->
        <Style x:Key="BasicButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Content="{TemplateBinding Content}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E0E0E0"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#D0D0D0"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="#F5F5F5"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="#E0E0E0"/>
                                <Setter Property="Foreground" Value="#A0A0A0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 执行按钮样式 -->
        <Style x:Key="ExecuteButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#0078D4"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#0078D4"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Content="{TemplateBinding Content}"
                                            TextElement.Foreground="{TemplateBinding Foreground}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#106EBE"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="#CCCCCC"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 取消按钮样式 -->
        <Style x:Key="CancelButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#D13438"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#D13438"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Content="{TemplateBinding Content}"
                                            TextElement.Foreground="{TemplateBinding Foreground}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#B71C1C"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#A00000"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="#CCCCCC"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 帮助按钮样式 -->
        <Style x:Key="HelpButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="22"/>
            <Setter Property="Height" Value="22"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="11"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Content="{TemplateBinding Content}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E0E0E0"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#D0D0D0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 文本框样式 -->
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="4,2"/>
            <Setter Property="Background" Value="White"/>
        </Style>
        
        <!-- 进度条样式 -->
        <Style x:Key="ProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Background" Value="#E0E0E0"/>
            <Setter Property="Foreground" Value="#0078D4"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>
        
        <!-- 日志文本框样式 -->
        <Style x:Key="LogTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#F8F8F8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontFamily" Value="Consolas, Courier New"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Padding" Value="5"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Margin="12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 面要素图层选择 -->
        <TextBlock Grid.Row="0" Grid.Column="0" Text="面要素图层:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="0" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <ComboBox Grid.Column="0" Height="22"
                    ItemsSource="{Binding PolygonLayers}"
                    SelectedItem="{Binding SelectedPolygonLayer}"
                    DisplayMemberPath="Name"
                    VerticalAlignment="Center"/>
            <Button Grid.Column="1" Width="22" Height="22" Margin="5,0,0,0"
                    Style="{StaticResource BasicButtonStyle}"
                    Command="{Binding RefreshLayersCommand}"
                    ToolTip="刷新图层列表"
                    VerticalAlignment="Center">
                <TextBlock Text="⟲" FontSize="14" FontWeight="Bold"
                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Button>
        </Grid>

        <!-- 检查容差值设置 -->
        <TextBlock Grid.Row="1" Grid.Column="0" Text="检查容差值:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="1" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBox Grid.Column="0" Text="{Binding Tolerance}" VerticalAlignment="Center" Height="22"
                    Style="{StaticResource TextBoxStyle}"/>
            <TextBlock Grid.Column="1" Text="m" VerticalAlignment="Center" Margin="5,0,0,0"/>
        </Grid>

        <!-- 输出面要素图层 -->
        <TextBlock Grid.Row="2" Grid.Column="0" Text="输出面要素图层:" VerticalAlignment="Center" Margin="0,0,10,10"/>
        <Grid Grid.Row="2" Grid.Column="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBox Grid.Column="0" Text="{Binding OutputPath}" VerticalAlignment="Center" Height="22"
                    Style="{StaticResource TextBoxStyle}"/>
            <Button Grid.Column="1" Content="浏览..." Height="22" Margin="5,0,0,0" Command="{Binding BrowseOutputCommand}"
                    Style="{StaticResource BasicButtonStyle}" VerticalAlignment="Center"/>
        </Grid>

        <!-- 进度条和日志区域 -->
        <Grid Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 进度条 -->
            <ProgressBar Grid.Row="0" Margin="0,0,0,5"
                       Style="{StaticResource ProgressBarStyle}"
                       Value="{Binding Progress}"
                       Minimum="0" Maximum="100"
                       IsIndeterminate="{Binding IsProgressIndeterminate}"
                       Height="6"/>

            <!-- 日志窗口 -->
            <Border Grid.Row="1"
                   BorderBrush="#CDCDCD" BorderThickness="1">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <TextBox
                          Style="{StaticResource LogTextBoxStyle}"
                          Text="{Binding LogContent, Mode=OneWay}"
                          BorderThickness="0"
                          VerticalAlignment="Stretch"
                          HorizontalAlignment="Stretch"
                          TextWrapping="Wrap"
                          AcceptsReturn="True"
                          IsReadOnly="True"
                          MinHeight="100"/>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- 状态消息和按钮区域 -->
        <Grid Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 执行状态 -->
            <TextBlock Grid.Row="0" Text="{Binding StatusMessage}" 
                    TextWrapping="Wrap" VerticalAlignment="Center" Margin="0,0,0,10"/>

            <!-- 按钮区域 -->
            <Border Grid.Row="1" BorderBrush="#E0E0E0"
                   BorderThickness="0,1,0,0"
                   Margin="0,3,0,0"
                   Padding="0,8,0,0">
                <Grid>
                    <Button Content="?" Width="22" Height="22"
                            Style="{StaticResource HelpButtonStyle}"
                            Command="{Binding ShowHelpCommand}"
                            ToolTip="查看工具使用说明"
                            HorizontalAlignment="Left"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <!-- 只在处理时显示停止按钮 -->
                        <Button Content="停止" Width="80" Command="{Binding CancelCommand}" Margin="0,0,10,0"
                                Style="{StaticResource CancelButtonStyle}"
                                Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                                IsEnabled="{Binding IsProcessing}"/>
                        <Button Content="开始" Width="80" Command="{Binding RunCommand}"
                                Style="{StaticResource ExecuteButtonStyle}"
                                IsEnabled="{Binding CanProcess}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>